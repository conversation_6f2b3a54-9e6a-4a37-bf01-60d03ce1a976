#include "../bladerf_stream.h"
#include <iostream>
#include <vector>
#include <cassert>
#include <chrono>
#include <thread>
#include <memory>

// Comprehensive test utilities
class ComprehensiveTestUtils {
public:
    // Check if BladeRF device is available
    static bool isBladeRFAvailable() {
        struct bladerf* device = nullptr;
        int status = bladerf_open(&device, nullptr);
        
        if (status == 0 && device != nullptr) {
            bladerf_close(device);
            return true;
        }
        
        return false;
    }
    
    // Test configuration validation
    static bool testConfigurationValidation() {
        std::cout << "Testing configuration validation..." << std::endl;
        
        // Test frequency ranges
        BladeRFIQStream::Config lowFreq(70000000, 1000000, 1500000, 30);   // 70 MHz
        BladeRFIQStream::Config highFreq(6000000000ULL, 1000000, 1500000, 30); // 6 GHz
        
        std::cout << "✓ Frequency range configurations created" << std::endl;
        
        // Test sample rate ranges
        BladeRFIQStream::Config lowRate(915000000, 160000, 200000, 30);     // 160 kHz
        BladeRFIQStream::Config highRate(915000000, 40000000, 50000000, 30); // 40 MHz
        
        std::cout << "✓ Sample rate range configurations created" << std::endl;
        
        // Test gain ranges
        BladeRFIQStream::Config lowGain(915000000, 1000000, 1500000, 0);    // 0 dB
        BladeRFIQStream::Config highGain(915000000, 1000000, 1500000, 76);  // 76 dB
        
        std::cout << "✓ Gain range configurations created" << std::endl;
        
        return true;
    }
};

// Test interface compliance
int test_interface_compliance() {
    std::cout << "\n--- Testing IIQStream Interface Compliance ---" << std::endl;
    
    try {
        // Test through interface pointer
        std::unique_ptr<IIQStream> stream = std::make_unique<BladeRFIQStream>();
        
        // Test interface methods
        if (stream->sourceName() != "bladeRF") {
            std::cout << "❌ Interface sourceName() failed" << std::endl;
            return 1;
        }
        std::cout << "✓ Interface sourceName() works: " << stream->sourceName() << std::endl;
        
        // Test sample rate (should return 0 or default when not configured)
        SampleRateType sampleRate = stream->sampleRate();
        std::cout << "✓ Interface sampleRate() works: " << sampleRate << std::endl;
        
        // Test isActive (should be false initially)
        if (stream->isActive()) {
            std::cout << "❌ Interface isActive() should return false initially" << std::endl;
            return 1;
        }
        std::cout << "✓ Interface isActive() works" << std::endl;
        
        // Test readSamples (should fail without device open)
        std::vector<RawIQSample> samples(10);
        if (stream->readSamples(samples.data(), 10)) {
            std::cout << "❌ Interface readSamples() should fail without open device" << std::endl;
            return 1;
        }
        std::cout << "✓ Interface readSamples() correctly fails without open device" << std::endl;
        
        // Test close (should not crash)
        stream->close();
        std::cout << "✓ Interface close() works" << std::endl;
        
        // Test lastError
        std::string error = stream->lastError();
        std::cout << "✓ Interface lastError() works: " << (error.empty() ? "(no error)" : error) << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in interface compliance test: " << e.what() << std::endl;
        return 1;
    }
}

// Test multiple stream instances
int test_multiple_streams() {
    std::cout << "\n--- Testing Multiple Stream Instances ---" << std::endl;
    
    try {
        // Create multiple stream instances with different configurations
        std::vector<std::unique_ptr<BladeRFIQStream>> streams;
        
        for (int i = 0; i < 3; ++i) {
            BladeRFIQStream::Config config(
                915000000 + i * 1000000,  // Different frequencies
                1000000 + i * 100000,     // Different sample rates
                1500000 + i * 100000,     // Different bandwidths
                30 + i * 5                // Different gains
            );
            
            streams.push_back(std::make_unique<BladeRFIQStream>(config));
        }
        
        std::cout << "✓ Created " << streams.size() << " stream instances" << std::endl;
        
        // Verify configurations
        for (size_t i = 0; i < streams.size(); ++i) {
            const auto& config = streams[i]->getConfig();
            uint64_t expectedFreq = 915000000 + i * 1000000;
            
            if (config.frequency != expectedFreq) {
                std::cout << "❌ Configuration mismatch for stream " << i << std::endl;
                return 1;
            }
        }
        std::cout << "✓ All stream configurations verified" << std::endl;
        
        // Test that only one can open the device at a time (if hardware available)
        if (ComprehensiveTestUtils::isBladeRFAvailable()) {
            bool firstOpened = streams[0]->open();
            if (firstOpened) {
                std::cout << "✓ First stream opened successfully" << std::endl;
                
                // Try to open second stream (should fail - device busy)
                bool secondOpened = streams[1]->open();
                if (secondOpened) {
                    std::cout << "⚠️  Second stream opened (unexpected - device should be busy)" << std::endl;
                    streams[1]->close();
                } else {
                    std::cout << "✓ Second stream correctly failed to open (device busy)" << std::endl;
                }
                
                streams[0]->close();
            } else {
                std::cout << "⚠️  No device available for multiple stream test" << std::endl;
            }
        } else {
            std::cout << "⚠️  No BladeRF device available - skipping hardware tests" << std::endl;
        }
        
        // Clean up
        streams.clear();
        std::cout << "✓ All streams cleaned up successfully" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in multiple streams test: " << e.what() << std::endl;
        return 1;
    }
}

// Test configuration edge cases
int test_configuration_edge_cases() {
    std::cout << "\n--- Testing Configuration Edge Cases ---" << std::endl;
    
    try {
        if (!ComprehensiveTestUtils::testConfigurationValidation()) {
            return 1;
        }
        
        // Test configuration updates on closed stream
        BladeRFIQStream stream;
        BladeRFIQStream::Config newConfig(433000000, 2000000, 2500000, 40);
        
        bool updateResult = stream.updateConfig(newConfig);
        if (updateResult) {
            std::cout << "⚠️  Configuration update succeeded on closed stream (unexpected)" << std::endl;
        } else {
            std::cout << "✓ Configuration update correctly failed on closed stream" << std::endl;
        }
        
        // Test configuration with extreme values
        BladeRFIQStream::Config extremeConfig(1, 1, 1, 0);
        BladeRFIQStream extremeStream(extremeConfig);
        std::cout << "✓ Extreme configuration handled without crash" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in configuration edge cases test: " << e.what() << std::endl;
        return 1;
    }
}

// Test streaming behavior
int test_streaming_behavior() {
    std::cout << "\n--- Testing Streaming Behavior ---" << std::endl;
    
    try {
        if (!ComprehensiveTestUtils::isBladeRFAvailable()) {
            std::cout << "⚠️  No BladeRF device available - skipping streaming tests" << std::endl;
            return 0;
        }
        
        BladeRFIQStream stream;
        
        if (!stream.open()) {
            std::cout << "⚠️  Cannot open device for streaming test: " << stream.lastError() << std::endl;
            return 0;
        }
        
        // Test continuous reading
        std::vector<RawIQSample> buffer(1000);
        int successfulReads = 0;
        const int maxReads = 10;
        
        for (int i = 0; i < maxReads; ++i) {
            if (stream.readSamples(buffer.data(), 1000)) {
                successfulReads++;
            } else {
                std::cout << "⚠️  Read failed at iteration " << i << ": " << stream.lastError() << std::endl;
                break;
            }
            
            // Small delay between reads
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
        
        if (successfulReads == maxReads) {
            std::cout << "✓ Continuous reading successful (" << successfulReads << "/" << maxReads << ")" << std::endl;
        } else {
            std::cout << "⚠️  Partial continuous reading (" << successfulReads << "/" << maxReads << ")" << std::endl;
        }
        
        // Test reading different buffer sizes
        std::vector<size_t> bufferSizes = {10, 100, 1000, 10000};
        
        for (size_t bufSize : bufferSizes) {
            std::vector<RawIQSample> testBuffer(bufSize);
            bool readResult = stream.readSamples(testBuffer.data(), bufSize);
            
            if (readResult) {
                std::cout << "✓ Successfully read " << bufSize << " samples" << std::endl;
            } else {
                std::cout << "❌ Failed to read " << bufSize << " samples: " << stream.lastError() << std::endl;
                return 1;
            }
        }
        
        stream.close();
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in streaming behavior test: " << e.what() << std::endl;
        return 1;
    }
}

// Test resource management
int test_resource_management() {
    std::cout << "\n--- Testing Resource Management ---" << std::endl;
    
    try {
        // Test rapid open/close cycles
        BladeRFIQStream stream;
        
        if (ComprehensiveTestUtils::isBladeRFAvailable()) {
            for (int i = 0; i < 5; ++i) {
                bool openResult = stream.open();
                if (openResult) {
                    // Quick read test
                    std::vector<RawIQSample> samples(100);
                    stream.readSamples(samples.data(), 100);
                    
                    stream.close();
                    std::cout << "✓ Open/close cycle " << (i + 1) << " completed" << std::endl;
                } else {
                    std::cout << "⚠️  Open failed on cycle " << (i + 1) << ": " << stream.lastError() << std::endl;
                    break;
                }
                
                // Small delay between cycles
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }
        } else {
            std::cout << "⚠️  No BladeRF device available - skipping resource management tests" << std::endl;
        }
        
        // Test destructor cleanup
        {
            BladeRFIQStream tempStream;
            if (ComprehensiveTestUtils::isBladeRFAvailable()) {
                tempStream.open();
                // Stream should be automatically closed by destructor
            }
        }
        std::cout << "✓ Destructor cleanup test completed" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in resource management test: " << e.what() << std::endl;
        return 1;
    }
}

// Main comprehensive test function
int run_comprehensive_tests() {
    std::cout << "Running comprehensive BladeRFIQStream tests..." << std::endl;
    
    int failures = 0;
    
    failures += test_interface_compliance();
    failures += test_multiple_streams();
    failures += test_configuration_edge_cases();
    failures += test_streaming_behavior();
    failures += test_resource_management();
    
    if (failures == 0) {
        std::cout << "\n🎉 All comprehensive tests passed!" << std::endl;
        std::cout << "\nComprehensive Features Verified:" << std::endl;
        std::cout << "  ✓ IIQStream interface compliance" << std::endl;
        std::cout << "  ✓ Multiple stream instance management" << std::endl;
        std::cout << "  ✓ Configuration edge cases" << std::endl;
        std::cout << "  ✓ Streaming behavior patterns" << std::endl;
        std::cout << "  ✓ Resource management and cleanup" << std::endl;
    } else {
        std::cout << "\n❌ " << failures << " comprehensive test(s) failed!" << std::endl;
    }
    
    return failures;
}
