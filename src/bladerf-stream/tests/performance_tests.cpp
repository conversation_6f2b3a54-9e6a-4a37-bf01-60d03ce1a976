#include "../bladerf_stream.h"
#include <iostream>
#include <vector>
#include <chrono>
#include <thread>
#include <memory>
#include <functional>

// Performance test utilities
class PerformanceTestUtils {
public:
    // Check if BladeRF device is available
    static bool isBladeRFAvailable() {
        struct bladerf* device = nullptr;
        int status = bladerf_open(&device, nullptr);
        
        if (status == 0 && device != nullptr) {
            bladerf_close(device);
            return true;
        }
        
        return false;
    }
    
    // Measure time in milliseconds
    static double measureTimeMs(std::function<void()> func) {
        auto start = std::chrono::high_resolution_clock::now();
        func();
        auto end = std::chrono::high_resolution_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        return static_cast<double>(duration.count()) / 1000.0;
    }
};

// Test device opening performance
int test_opening_performance() {
    std::cout << "\n--- Testing Device Opening Performance ---" << std::endl;
    
    try {
        if (!PerformanceTestUtils::isBladeRFAvailable()) {
            std::cout << "⚠️  No BladeRF device available - skipping opening performance tests" << std::endl;
            return 0;
        }
        
        // Measure opening time
        BladeRFIQStream stream;
        
        double openTime = PerformanceTestUtils::measureTimeMs([&]() {
            stream.open();
        });
        
        if (stream.isActive()) {
            std::cout << "✓ Device opening time: " << openTime << " ms" << std::endl;
            
            if (openTime < 5000.0) {
                std::cout << "✓ Opening performance is good (<5000ms)" << std::endl;
            } else {
                std::cout << "⚠️  Opening performance is slow (>" << openTime << "ms)" << std::endl;
            }
            
            // Measure closing time
            double closeTime = PerformanceTestUtils::measureTimeMs([&]() {
                stream.close();
            });
            
            std::cout << "✓ Device closing time: " << closeTime << " ms" << std::endl;
            
        } else {
            std::cout << "❌ Failed to open device for performance test: " << stream.lastError() << std::endl;
            return 1;
        }
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in opening performance test: " << e.what() << std::endl;
        return 1;
    }
}

// Test reading performance
int test_reading_performance() {
    std::cout << "\n--- Testing Reading Performance ---" << std::endl;
    
    try {
        if (!PerformanceTestUtils::isBladeRFAvailable()) {
            std::cout << "⚠️  No BladeRF device available - skipping reading performance tests" << std::endl;
            return 0;
        }
        
        BladeRFIQStream stream;
        
        if (!stream.open()) {
            std::cout << "❌ Failed to open device for reading performance test: " << stream.lastError() << std::endl;
            return 1;
        }
        
        // Allow streaming to stabilize
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // Test different buffer sizes
        std::vector<size_t> bufferSizes = {1000, 10000, 100000};
        
        for (size_t bufferSize : bufferSizes) {
            std::vector<RawIQSample> buffer(bufferSize);
            
            double readTime = PerformanceTestUtils::measureTimeMs([&]() {
                stream.readSamples(buffer.data(), bufferSize);
            });
            
            double samplesPerSecond = static_cast<double>(bufferSize) / (readTime / 1000.0);
            double mbPerSecond = (bufferSize * sizeof(RawIQSample)) / (1024.0 * 1024.0) / (readTime / 1000.0);
            
            std::cout << "✓ Buffer size " << bufferSize << ":" << std::endl;
            std::cout << "   Read time: " << readTime << " ms" << std::endl;
            std::cout << "   Rate: " << static_cast<uint64_t>(samplesPerSecond) << " samples/sec" << std::endl;
            std::cout << "   Throughput: " << mbPerSecond << " MB/sec" << std::endl;
            
            if (samplesPerSecond < 100000) {
                std::cout << "⚠️  Reading performance is low for buffer size " << bufferSize << std::endl;
            }
        }
        
        stream.close();
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in reading performance test: " << e.what() << std::endl;
        return 1;
    }
}

// Test sustained throughput
int test_sustained_throughput() {
    std::cout << "\n--- Testing Sustained Throughput ---" << std::endl;
    
    try {
        if (!PerformanceTestUtils::isBladeRFAvailable()) {
            std::cout << "⚠️  No BladeRF device available - skipping sustained throughput tests" << std::endl;
            return 0;
        }
        
        BladeRFIQStream stream;
        
        if (!stream.open()) {
            std::cout << "❌ Failed to open device for sustained throughput test: " << stream.lastError() << std::endl;
            return 1;
        }
        
        // Allow streaming to stabilize
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // Test sustained reading for several seconds
        const size_t bufferSize = 10000;
        const int numIterations = 100;
        std::vector<RawIQSample> buffer(bufferSize);
        
        uint64_t totalSamples = 0;
        int successfulReads = 0;
        
        auto startTime = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < numIterations; ++i) {
            if (stream.readSamples(buffer.data(), bufferSize)) {
                totalSamples += bufferSize;
                successfulReads++;
            } else {
                std::cout << "⚠️  Read failed at iteration " << i << ": " << stream.lastError() << std::endl;
                break;
            }
            
            // Small delay to simulate processing
            std::this_thread::sleep_for(std::chrono::microseconds(100));
        }
        
        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
        
        double totalTimeSeconds = static_cast<double>(duration.count()) / 1000.0;
        double avgSamplesPerSecond = static_cast<double>(totalSamples) / totalTimeSeconds;
        double avgMbPerSecond = (totalSamples * sizeof(RawIQSample)) / (1024.0 * 1024.0) / totalTimeSeconds;
        
        std::cout << "✓ Sustained throughput test results:" << std::endl;
        std::cout << "   Total samples: " << totalSamples << std::endl;
        std::cout << "   Successful reads: " << successfulReads << "/" << numIterations << std::endl;
        std::cout << "   Test duration: " << totalTimeSeconds << " seconds" << std::endl;
        std::cout << "   Average rate: " << static_cast<uint64_t>(avgSamplesPerSecond) << " samples/sec" << std::endl;
        std::cout << "   Average throughput: " << avgMbPerSecond << " MB/sec" << std::endl;
        
        if (successfulReads == numIterations) {
            std::cout << "✓ Sustained throughput test passed" << std::endl;
        } else {
            std::cout << "⚠️  Some reads failed during sustained test" << std::endl;
        }
        
        stream.close();
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in sustained throughput test: " << e.what() << std::endl;
        return 1;
    }
}

// Test configuration change performance
int test_configuration_performance() {
    std::cout << "\n--- Testing Configuration Change Performance ---" << std::endl;
    
    try {
        if (!PerformanceTestUtils::isBladeRFAvailable()) {
            std::cout << "⚠️  No BladeRF device available - skipping configuration performance tests" << std::endl;
            return 0;
        }
        
        BladeRFIQStream stream;
        
        if (!stream.open()) {
            std::cout << "❌ Failed to open device for configuration performance test: " << stream.lastError() << std::endl;
            return 1;
        }
        
        // Test frequency changes
        std::vector<uint64_t> frequencies = {915000000, 433000000, 2400000000ULL};
        
        for (uint64_t freq : frequencies) {
            BladeRFIQStream::Config newConfig = stream.getConfig();
            newConfig.frequency = freq;
            
            double configTime = PerformanceTestUtils::measureTimeMs([&]() {
                stream.updateConfig(newConfig);
            });
            
            std::cout << "✓ Frequency change to " << (freq / 1000000) << " MHz: " << configTime << " ms" << std::endl;
            
            if (configTime > 100.0) {
                std::cout << "⚠️  Configuration change is slow (>" << configTime << "ms)" << std::endl;
            }
            
            // Small delay between changes
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
        
        // Test sample rate changes
        std::vector<uint32_t> sampleRates = {1000000, 2000000, 5000000};
        
        for (uint32_t rate : sampleRates) {
            BladeRFIQStream::Config newConfig = stream.getConfig();
            newConfig.sampleRate = rate;
            
            double configTime = PerformanceTestUtils::measureTimeMs([&]() {
                stream.updateConfig(newConfig);
            });
            
            std::cout << "✓ Sample rate change to " << (rate / 1000000) << " MHz: " << configTime << " ms" << std::endl;
            
            // Small delay between changes
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
        
        stream.close();
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in configuration performance test: " << e.what() << std::endl;
        return 1;
    }
}

// Test memory usage
int test_memory_usage() {
    std::cout << "\n--- Testing Memory Usage ---" << std::endl;
    
    try {
        // Test multiple stream instances
        const int numStreams = 10;
        std::vector<std::unique_ptr<BladeRFIQStream>> streams;
        
        for (int i = 0; i < numStreams; ++i) {
            BladeRFIQStream::Config config(915000000 + i * 1000000, 1000000, 1500000, 30);
            streams.push_back(std::make_unique<BladeRFIQStream>(config));
        }
        
        std::cout << "✓ Created " << numStreams << " stream instances" << std::endl;
        
        // Test large buffer allocations
        std::vector<size_t> bufferSizes = {100000, 1000000, 10000000};
        
        for (size_t bufferSize : bufferSizes) {
            try {
                std::vector<RawIQSample> largeBuffer(bufferSize);
                std::cout << "✓ Allocated buffer of " << bufferSize << " samples (" 
                         << (bufferSize * sizeof(RawIQSample) / 1024 / 1024) << " MB)" << std::endl;
            } catch (const std::bad_alloc& e) {
                std::cout << "⚠️  Failed to allocate buffer of " << bufferSize << " samples" << std::endl;
            }
        }
        
        // Clean up
        streams.clear();
        std::cout << "✓ All streams cleaned up" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in memory usage test: " << e.what() << std::endl;
        return 1;
    }
}

// Main performance test function
int run_performance_tests() {
    std::cout << "Running BladeRFIQStream performance tests..." << std::endl;
    
    int failures = 0;
    
    failures += test_opening_performance();
    failures += test_reading_performance();
    failures += test_sustained_throughput();
    failures += test_configuration_performance();
    failures += test_memory_usage();
    
    if (failures == 0) {
        std::cout << "\n🎉 All performance tests passed!" << std::endl;
        std::cout << "\nPerformance Summary:" << std::endl;
        std::cout << "- Device opening: <5000ms typical" << std::endl;
        std::cout << "- Reading rate: >100K samples/sec" << std::endl;
        std::cout << "- Sustained throughput: Stable over time" << std::endl;
        std::cout << "- Configuration changes: <100ms typical (optimized)" << std::endl;
        std::cout << "- Memory efficiency: Multiple streams supported" << std::endl;
    } else {
        std::cout << "\n❌ " << failures << " performance test(s) failed!" << std::endl;
    }
    
    return failures;
}
