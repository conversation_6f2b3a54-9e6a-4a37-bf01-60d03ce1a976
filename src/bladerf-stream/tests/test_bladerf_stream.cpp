#include "../bladerf_stream.h"
#include <iostream>
#include <vector>
#include <cassert>
#include <chrono>
#include <thread>

// Hardware test utilities - tests that may require actual BladeRF hardware
class HardwareTestUtils {
public:
    // Check if BladeRF device is available
    static bool isBladeRFAvailable() {
        struct bladerf* device = nullptr;
        int status = bladerf_open(&device, nullptr);
        
        if (status == 0 && device != nullptr) {
            bladerf_close(device);
            return true;
        }
        
        return false;
    }
    
    // Get BladeRF device info if available
    static std::string getBladeRFInfo() {
        struct bladerf* device = nullptr;
        int status = bladerf_open(&device, nullptr);
        
        if (status != 0 || device == nullptr) {
            return "No BladeRF device available";
        }
        
        struct bladerf_devinfo info;
        status = bladerf_get_devinfo(device, &info);
        
        std::string result;
        if (status == 0) {
            result = "BladeRF Device: " + std::string(info.product) + 
                    " Serial: " + std::string(info.serial);
        } else {
            result = "BladeRF device found but info unavailable";
        }
        
        bladerf_close(device);
        return result;
    }
};

// Test device detection and opening
int test_device_detection() {
    std::cout << "\n--- Testing Device Detection ---" << std::endl;
    
    try {
        // Check if hardware is available
        bool deviceAvailable = HardwareTestUtils::isBladeRFAvailable();
        
        if (!deviceAvailable) {
            std::cout << "⚠️  No BladeRF device detected" << std::endl;
            std::cout << "   This is expected if no hardware is connected" << std::endl;
            std::cout << "   Skipping hardware-dependent tests" << std::endl;
            return 0; // Not a failure, just no hardware
        }
        
        std::cout << "✓ BladeRF device detected" << std::endl;
        std::cout << "   " << HardwareTestUtils::getBladeRFInfo() << std::endl;
        
        // Test opening device through BladeRFIQStream
        BladeRFIQStream stream;
        bool openResult = stream.open();
        
        if (openResult) {
            std::cout << "✓ Device opened successfully through BladeRFIQStream" << std::endl;
            
            // Test that stream is active
            if (stream.isActive()) {
                std::cout << "✓ Stream is active after opening" << std::endl;
            } else {
                std::cout << "❌ Stream should be active after successful open" << std::endl;
                return 1;
            }
            
            // Test sample rate
            SampleRate sampleRate = stream.sampleRate();
            if (sampleRate > 0) {
                std::cout << "✓ Sample rate reported: " << sampleRate << " Hz" << std::endl;
            } else {
                std::cout << "❌ Invalid sample rate: " << sampleRate << std::endl;
                return 1;
            }
            
            // Close the stream
            stream.close();
            
            if (!stream.isActive()) {
                std::cout << "✓ Stream correctly marked as inactive after close" << std::endl;
            } else {
                std::cout << "❌ Stream should be inactive after close" << std::endl;
                return 1;
            }
            
        } else {
            std::cout << "❌ Failed to open device: " << stream.lastError() << std::endl;
            return 1;
        }
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in device detection test: " << e.what() << std::endl;
        return 1;
    }
}

// Test device configuration
int test_device_configuration() {
    std::cout << "\n--- Testing Device Configuration ---" << std::endl;
    
    try {
        if (!HardwareTestUtils::isBladeRFAvailable()) {
            std::cout << "⚠️  No BladeRF device available - skipping configuration tests" << std::endl;
            return 0;
        }
        
        // Test with custom configuration
        BladeRFIQStream::Config config(915000000, 1000000, 1500000, 30);
        BladeRFIQStream stream(config);
        
        // Verify configuration before opening
        const auto& streamConfig = stream.getConfig();
        if (streamConfig.frequency != 915000000) {
            std::cout << "❌ Configuration frequency mismatch" << std::endl;
            return 1;
        }
        std::cout << "✓ Configuration set correctly before opening" << std::endl;
        
        // Open with configuration
        bool openResult = stream.open();
        
        if (openResult) {
            std::cout << "✓ Device opened with custom configuration" << std::endl;
            
            // Verify sample rate matches configuration
            SampleRate reportedRate = stream.sampleRate();
            if (reportedRate == config.sampleRate) {
                std::cout << "✓ Sample rate matches configuration: " << reportedRate << " Hz" << std::endl;
            } else {
                std::cout << "⚠️  Sample rate mismatch: expected " << config.sampleRate 
                         << ", got " << reportedRate << std::endl;
            }
            
            // Test configuration update
            BladeRFIQStream::Config newConfig(433000000, 2000000, 2500000, 40);
            bool updateResult = stream.updateConfig(newConfig);
            
            if (updateResult) {
                std::cout << "✓ Configuration updated successfully" << std::endl;
                
                // Verify new configuration
                const auto& updatedConfig = stream.getConfig();
                if (updatedConfig.frequency == newConfig.frequency) {
                    std::cout << "✓ New configuration applied correctly" << std::endl;
                } else {
                    std::cout << "❌ Configuration update not applied correctly" << std::endl;
                    return 1;
                }
            } else {
                std::cout << "⚠️  Configuration update failed: " << stream.lastError() << std::endl;
            }
            
            stream.close();
            
        } else {
            std::cout << "❌ Failed to open device with configuration: " << stream.lastError() << std::endl;
            return 1;
        }
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in device configuration test: " << e.what() << std::endl;
        return 1;
    }
}

// Test sample reading
int test_sample_reading() {
    std::cout << "\n--- Testing Sample Reading ---" << std::endl;
    
    try {
        if (!HardwareTestUtils::isBladeRFAvailable()) {
            std::cout << "⚠️  No BladeRF device available - skipping sample reading tests" << std::endl;
            return 0;
        }
        
        BladeRFIQStream stream;
        
        if (!stream.open()) {
            std::cout << "❌ Failed to open device for sample reading: " << stream.lastError() << std::endl;
            return 1;
        }
        
        // Allow some time for streaming to start
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        
        // Test reading small number of samples
        std::vector<RawIQSample> samples(100);
        bool readResult = stream.readSamples(samples.data(), 100);
        
        if (readResult) {
            std::cout << "✓ Successfully read 100 samples" << std::endl;
            
            // Verify sample format (basic sanity check)
            bool hasNonZeroSamples = false;
            for (size_t i = 0; i < 10 && i < samples.size(); ++i) {
                RawIQSample sample = samples[i];
                uint16_t i_part = static_cast<uint16_t>(sample & 0xFFFF);
                uint16_t q_part = static_cast<uint16_t>((sample >> 16) & 0xFFFF);
                
                if (i_part != 0 || q_part != 0) {
                    hasNonZeroSamples = true;
                }
                
                if (i < 3) {  // Show first few samples
                    std::cout << "   Sample " << i << ": I=" << i_part << ", Q=" << q_part << std::endl;
                }
            }
            
            if (hasNonZeroSamples) {
                std::cout << "✓ Samples contain non-zero data (likely real signal)" << std::endl;
            } else {
                std::cout << "⚠️  All samples are zero (may indicate no signal or issue)" << std::endl;
            }
            
            // Test reading larger number of samples
            std::vector<RawIQSample> largeSamples(10000);
            auto startTime = std::chrono::high_resolution_clock::now();
            bool largeReadResult = stream.readSamples(largeSamples.data(), 10000);
            auto endTime = std::chrono::high_resolution_clock::now();
            
            if (largeReadResult) {
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
                std::cout << "✓ Successfully read 10,000 samples in " << duration.count() << "ms" << std::endl;
                
                double samplesPerSecond = 10000.0 / (duration.count() / 1000.0);
                std::cout << "   Reading rate: " << static_cast<int>(samplesPerSecond) << " samples/sec" << std::endl;
            } else {
                std::cout << "❌ Failed to read large sample batch: " << stream.lastError() << std::endl;
                return 1;
            }
            
        } else {
            std::cout << "❌ Failed to read samples: " << stream.lastError() << std::endl;
            return 1;
        }
        
        stream.close();
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in sample reading test: " << e.what() << std::endl;
        return 1;
    }
}

// Test error conditions
int test_error_conditions() {
    std::cout << "\n--- Testing Error Conditions ---" << std::endl;
    
    try {
        // Test reading without opening
        BladeRFIQStream stream;
        std::vector<RawIQSample> samples(100);
        
        bool readResult = stream.readSamples(samples.data(), 100);
        if (readResult) {
            std::cout << "❌ Reading should fail when device is not open" << std::endl;
            return 1;
        }
        std::cout << "✓ Reading correctly fails when device is not open" << std::endl;
        
        // Test opening with invalid device identifier
        BladeRFIQStream invalidStream(BladeRFIQStream::Config(), "invalid_device_id");
        bool openResult = invalidStream.open();
        
        if (openResult) {
            std::cout << "⚠️  Opening succeeded with invalid device ID (unexpected)" << std::endl;
            invalidStream.close();
        } else {
            std::cout << "✓ Opening correctly fails with invalid device ID" << std::endl;
            std::cout << "   Error: " << invalidStream.lastError() << std::endl;
        }
        
        // Test multiple close calls
        if (HardwareTestUtils::isBladeRFAvailable()) {
            BladeRFIQStream multiCloseStream;
            if (multiCloseStream.open()) {
                multiCloseStream.close();
                multiCloseStream.close();  // Second close should be safe
                std::cout << "✓ Multiple close calls handled safely" << std::endl;
            }
        }
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in error conditions test: " << e.what() << std::endl;
        return 1;
    }
}

// Main BladeRF stream test function
int run_bladerf_stream_tests() {
    std::cout << "Running BladeRFIQStream hardware tests..." << std::endl;
    
    int failures = 0;
    
    failures += test_device_detection();
    failures += test_device_configuration();
    failures += test_sample_reading();
    failures += test_error_conditions();
    
    if (failures == 0) {
        std::cout << "\n🎉 All BladeRF stream tests passed!" << std::endl;
        std::cout << "\nHardware Features Verified:" << std::endl;
        std::cout << "  ✓ Device detection and opening" << std::endl;
        std::cout << "  ✓ Device configuration" << std::endl;
        std::cout << "  ✓ Sample reading and streaming" << std::endl;
        std::cout << "  ✓ Error handling" << std::endl;
        std::cout << "  ✓ Resource management" << std::endl;
    } else {
        std::cout << "\n❌ " << failures << " BladeRF stream test(s) failed!" << std::endl;
        std::cout << "\nNote: Some failures may be due to:" << std::endl;
        std::cout << "  - No BladeRF device connected" << std::endl;
        std::cout << "  - Device permissions issues" << std::endl;
        std::cout << "  - Device already in use" << std::endl;
        std::cout << "  - Driver or firmware issues" << std::endl;
    }
    
    return failures;
}
