#include "./frame_composition_node.h"
#include "logging/logging.h"
#include <iostream>
#include <sstream>
#include <cmath>
#include "../../video_processor_configs.h"

namespace IQVideoProcessor::Pipeline {

FrameCompositionNode::FrameCompositionNode(const SampleRate sampleRate): sampleRate_(sampleRate) {
  setRunning();
}

FrameCompositionNode::~FrameCompositionNode() {
  PipelineComponent::stop();
}

bool FrameCompositionNode::process(LineDetectionEvent& event) {
  if (!running()) return false;

  // Route events to appropriate handlers based on event type
  switch (event.type) {
    case LineDetectionEventType::STANDARD_DETECTED:
      handleStandardDetected(event.videoStandard);
      break;
    case LineDetectionEventType::SYNC_LOCKED:
      handleSyncLock();
      break;
    case LineDetectionEventType::SYNC_LOCK_LOST:
      handleSyncLockLost();
      break;
    case LineDetectionEventType::FRAME_FIELD_BEGIN:
      handleFrameFieldBegin(event.frameFieldNumber, event.isTopField);
      break;
    case LineDetectionEventType::FRAME_FIELD_END:
      handleFrameFieldEnd(event.frameFieldNumber, event.isTopField);
      break;
    case LineDetectionEventType::LINE_RECEIVED:
      handleLineReceived(event.data, event.dataSize, event.lineNumber, event.isTopField);
      break;
    case LineDetectionEventType::EQUALIZATION:
      handleEqualization(event.data, event.dataSize);
      break;
    case LineDetectionEventType::UNKNOWN:
    default:
      handleUnknownEvent();
      break;
  }

  return running();
}

void FrameCompositionNode::handleStandardDetected(const VideoStandard standard) {
  if (standard == UNKNOWN_VIDEO_STANDARD) {
    isStandardDetected_ = false;
    currentStandard_ = UNKNOWN_VIDEO_STANDARD;
    frameCanvas_.reset();
    LOG_ERROR(FRAME_COMPOSITION_NODE, "unknown video standard received");
    return;
  }

  if (currentStandard_ == standard) return; // No change
  LOG_VERBOSE(FRAME_COMPOSITION_NODE, "switching video standard from " << currentStandard_ << " to " << standard);

  // Prepare the frame canvas for the new standard, allocating necessary resources
  auto [width, height] = getVideoStandardDimensions(standard);
  frameCanvas_.reset();
  frameCanvas_ = std::make_unique<FrameCanvas>(width, height);
  currentStandard_ = standard;
  isInterlaced_ = isVideoStandardInterlaced(standard);
  isTFF_ = isVideoStandardTFF(standard);
  auto [leftPaddingSec, rightPaddingSec] = getVideoStandardPaddings(standard);
  lineLeftPadding_ = static_cast<size_t>(std::ceil(leftPaddingSec * sampleRate_));
  lineRightPadding_ = static_cast<size_t>(std::ceil(rightPaddingSec * sampleRate_));
  lineHorizontalPadding_ = lineLeftPadding_ + lineRightPadding_;

  // Prepare the composed frame structure, reserving space for rendered data
  currentCompositionResult_.data.resize(frameCanvas_->getMinRenderBufferSize());
  currentCompositionResult_.width = width;
  currentCompositionResult_.height = height;
  currentCompositionResult_.videoStandard = standard;
  currentCompositionResult_.frameNumber = 0;
  currentCompositionResult_.dataSize = 0;

  equalizationBlackLevelCalcRegionSamples_ = static_cast<size_t>(std::ceil(EQUALIZATION_BLACK_LEVEL_REGION_SEC * sampleRate_));
  equalizationBlackLevelCalcRegionOffset_ = static_cast<size_t>(std::ceil(EQUALIZATION_BLACK_LEVEL_OFFSET_SEC * sampleRate_));

  equalizationBlackLevel_ = 0.0f; // TODO Reset to default or calculated value
  isStandardDetected_ = true;

  // TODO: EMIT THE BLANK FRAME FOR THE NEW STANDARD
}

void FrameCompositionNode::handleSyncLock() {
  LOG_VERBOSE(FRAME_COMPOSITION_NODE, "sync lock acquired");
  isSyncLocked_ = true;
}

void FrameCompositionNode::handleSyncLockLost() {
  LOG_VERBOSE(FRAME_COMPOSITION_NODE, "sync lock lost");
  isSyncLocked_ = false;
  // TODO Handle the already received incomplete frame if needed
}

void FrameCompositionNode::handleFrameFieldBegin(const size_t frameFieldNumber, const bool isTopField) {
  if (!isStandardDetected_ || !isSyncLocked_) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "warning - frame field begin received but standard not detected or sync not locked");
    return;
  }

  if (!currentProcessingFrame_.has_value()) {
    if (!isTopField) {
      LOG_VERBOSE(FRAME_COMPOSITION_NODE, "waiting for top field to start frame processing...");
      isSyncingWithTopField_ = true;
      return; // We expect the first part to be top field in any case, so ignore bottom field part if no frame is active
    }
    isSyncingWithTopField_ = false;
    currentProcessingFrame_ = FrameInfo{frameFieldNumber, true, false};
  } else {
    if (isTopField || !isInterlaced_) {
      isSyncingWithTopField_ = false;
      // We can't have the second top field part or normally reach here in interlaced mode
      processCompleteFrame(); // So we end the previous frame first, and begin a new one starting from top field part
      currentProcessingFrame_ = FrameInfo{frameFieldNumber, true, false};
      LOG_VERBOSE(FRAME_COMPOSITION_NODE, "begin processing next top field in series!");
      return;
    }
    // We are receiving the bottom field part of the current frame
    auto &cfi = currentProcessingFrame_.value();
    cfi.hasBottomFieldPart = true;
    // std::cout << "FrameCompositionNode: Frame " << frameFieldNumber << " begin (BOTTOM field)" << std::endl;
  }
}

void FrameCompositionNode::handleFrameFieldEnd(const size_t frameFieldNumber, const bool isTopField) {
  // We don't care about the detected standard or sync state here, just validate frame field info
  if (!currentProcessingFrame_.has_value()) return;

  const auto &cfi = currentProcessingFrame_.value(); // Copy current frame field info
  // Ensure we have both fields for interlaced mode, or at least top field for regular mode
  if ((cfi.hasTopFieldPart && cfi.hasBottomFieldPart) || (!isInterlaced_ && cfi.hasTopFieldPart)) {
    processCompleteFrame(); // Frame is complete, lets process it
  }
}

void FrameCompositionNode::handleLineReceived(
  const std::vector<TFloat> &data,
  const size_t dataSize,
  const size_t lineNumber,
  const bool isTopField
) {
  // Sanity checks
  if (!isStandardDetected_) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "line received but standard is not detected");
    return;
  }
  if (!currentProcessingFrame_.has_value()) {
    if (!isSyncingWithTopField_) {
      LOG_ERROR(FRAME_COMPOSITION_NODE, "line received but no frame field is active");
    }
    return;
  }

  const auto croppedDataStart = lineLeftPadding_;
  const auto croppedDataSize = dataSize > lineHorizontalPadding_ ? dataSize - lineHorizontalPadding_ : 0;
  if (croppedDataSize == 0) {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "line received but padding exceeds data size");
    return;
  }

  if (isInterlaced_) {
    const auto lineIndex = (lineNumber << 1) + (isTFF_ == isTopField ? 1 : 0);
    frameCanvas_->setVideoLineRawData(lineIndex, &data[croppedDataStart], croppedDataSize);
  } else {
    frameCanvas_->setVideoLineRawData(lineNumber, &data[croppedDataStart], croppedDataSize);
  }

  // std::cout << "FrameCompositionNode: Line " << lineNumber << " received ("<< (isTopField ? "TOP FIELD" : "BOTTOM FIELD") << " field), " << dataSize << " samples" << std::endl;
}

void FrameCompositionNode::handleEqualization(const std::vector<TFloat>& data, const size_t dataSize) {
  // TODO Calculate the average level and accumulate over multiple equalization pulses for better accuracy in more safe manner
  if (equalizationBlackLevelCalcRegionOffset_ + equalizationBlackLevelCalcRegionSamples_ > dataSize) {
    // We don't have enough data to calculate the black level
    LOG_ERROR(FRAME_COMPOSITION_NODE, "equalization data received but not enough samples to calculate black level");
    equalizationBlackLevel_ = data[dataSize >> 1]; // Take the middle sample as black level
    return;
  }
  // Calculate average black level from the defined region
  double accumulator = 0.0f;
  const auto* blackRegionData = &data[equalizationBlackLevelCalcRegionOffset_];
  for (size_t i = 0; i < equalizationBlackLevelCalcRegionSamples_; i++) {
    accumulator += blackRegionData[i];
  }
  equalizationBlackLevel_ = static_cast<TFloat>(accumulator / static_cast<double>(equalizationBlackLevelCalcRegionSamples_));
}

void FrameCompositionNode::handleUnknownEvent() {
  LOG_ERROR(FRAME_COMPOSITION_NODE, "unknown event received");
}

void FrameCompositionNode::processCompleteFrame() {
  if (!currentProcessingFrame_.has_value()) return; // No frame to process
  const auto &cfi = currentProcessingFrame_.value(); // Copy current frame field info

  const auto renderResult = frameCanvas_->render(
    &currentCompositionResult_.data[0],
    currentCompositionResult_.data.size(),
    equalizationBlackLevel_
  );

  if (renderResult.has_value()) {
    currentCompositionResult_.dataSize = renderResult.value();
    currentCompositionResult_.frameNumber = frameCounter_++;

    if (!sendOutput(currentCompositionResult_)) {
      stop();
    }
  } else {
    LOG_ERROR(FRAME_COMPOSITION_NODE, "failed to process frame, initial field: " << cfi.initialFrameNumber);
  }

  currentProcessingFrame_.reset(); // Clear current frame field info
}

void FrameCompositionNode::processInitialFrame() {
  
}

}
