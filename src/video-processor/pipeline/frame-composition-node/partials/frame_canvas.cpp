#include "./frame_canvas.h"
#include "logging/logging.h"
#include <algorithm>
#include <cassert>
#include <cmath>
#include <cstring>
#include <fstream>
#include <iostream>
#include <turbojpeg.h>

namespace IQVideoProcessor::Pipeline {

FrameCanvas::~FrameCanvas() {
  // Clean up TurboJPEG compressor
  if (tjHandle_) {
    tjD<PERSON>roy(tjHandle_);
    tjHandle_ = nullptr;
  }
}

FrameCanvas::FrameCanvas(const size_t width, const size_t height): width_(width), height_(height) {
  totalPixels_ = width * height;
  assert(totalPixels_ > 0);

  // Pre-allocate raw buffer
  rawBuffer_ = std::make_unique<TFloat[]>(totalPixels_);
  std::fill_n(rawBuffer_.get(), totalPixels_, 0);

  // Pre-allocate grayscale conversion buffer
  grayscaleBuffer_ = std::make_unique<uint8_t[]>(totalPixels_);

  minRenderBufferSize_ = totalPixels_ << 1;

  // Initialize TurboJPEG compressor once
  tjHandle_ = tjInitCompress();
  if (!tjHandle_) {
    LOG_ERROR(FRAME_CANVAS, "failed to initialize TurboJPEG compressor: " << tjGetErrorStr());
  }
}

size_t FrameCanvas::getMinRenderBufferSize() const {
  return minRenderBufferSize_;
}

void FrameCanvas::setVideoLineRawData(const size_t lineIndex, const TFloat *data, const size_t dataSize) {
  if (lineIndex >= height_ || dataSize == 0) return;

  // Calculate the starting position in the raw buffer for this line
  TFloat* lineStart = &rawBuffer_.get()[lineIndex * width_];

  if (dataSize == width_) {
    std::memcpy(lineStart, data, width_ * sizeof(TFloat)); // Direct copy if sizes match
    return;
  }

  // Linear interpolation resize
  if (dataSize == 1) {
    std::fill_n(lineStart, width_, data[0]);     // Special case: fill entire line with single value
    return;
  }

  const auto scale = static_cast<TFloat>(dataSize - 1) / static_cast<TFloat>(width_ - 1);

  for (size_t i = 0; i < width_; ++i) {
    const auto pos = static_cast<TFloat>(i) * scale;
    const auto idx = static_cast<size_t>(pos);
    const TFloat frac = pos - static_cast<TFloat>(idx);

    if (idx >= dataSize - 1) {
      lineStart[i] = data[dataSize - 1];
    } else {
      lineStart[i] = data[idx] * (static_cast<TFloat>(1.0) - frac) + data[idx + 1] * frac;
    }
  }
}

std::optional<size_t> FrameCanvas::render(uint8_t* outputBuffer, const size_t outputBufferSize, TFloat blackLevel, TFloat whiteLevel) {
  // Convert raw buffer to grayscale using pre-allocated buffer
  const uint8_t* grayscaleData = convertToGrayscale(blackLevel, whiteLevel);

  // Export to JPEG with high quality (100 - to avoid compression)
  auto jpegSize = exportGrayscaleToJpeg(outputBuffer, outputBufferSize, grayscaleData, 75);
  if (!jpegSize) {
    return std::nullopt;
  }

  return jpegSize;
}

const uint8_t* FrameCanvas::convertToGrayscale(const TFloat blackLevel, const TFloat whiteLevel) {
  // Calculate scaling factor
  const TFloat range = whiteLevel - blackLevel;
  if (range <= 0) {
    // Invalid range, fill with middle gray
    std::fill_n(grayscaleBuffer_.get(), totalPixels_, 128);
    return grayscaleBuffer_.get();
  }

  const auto scale = static_cast<TFloat>(255.0f) / range;

  // Convert each pixel using pre-allocated buffer
  for (size_t i = 0; i < totalPixels_; ++i) {
    const auto value = rawBuffer_[i];
    // Clamp to [blackLevel, whiteLevel] range
    if (value <= blackLevel) {
      grayscaleBuffer_[i] = 0;
    } else if (value >= whiteLevel) {
      grayscaleBuffer_[i] = 255;
    } else {
      // Scale to [0, 255] range
      const auto scaled = (value - blackLevel) * scale;
      grayscaleBuffer_[i] = static_cast<uint8_t>(std::round(scaled));
    }
  }

  return grayscaleBuffer_.get();
}

size_t FrameCanvas::exportGrayscaleToJpeg(uint8_t* outputBuffer, const size_t outputBufferSize, const uint8_t* imageData, int quality) const {
  // Sanity checks
  if (!tjHandle_) {
    LOG_ERROR(FRAME_CANVAS, "uninitialized TurboJPEG compressor");
    return 0;
  }

  // Clamp quality to valid range
  quality = std::max(1, std::min(100, quality));

  // Compress the image using provided buffer
  auto jpegSize = outputBufferSize;
  auto* jpegBufPtr = outputBuffer;
  const auto result = tjCompress2(
    tjHandle_,
    imageData,                    // Source image data
    static_cast<int>(width_),     // Image width
    0,                            // Pitch (0 = width * pixel size)
    static_cast<int>(height_),    // Image height
    TJPF_GRAY,                    // Pixel format (grayscale)
    &jpegBufPtr,                  // Output JPEG buffer
    &jpegSize,                    // JPEG size (input: buffer size, output: actual size)
    TJSAMP_GRAY,                  // Subsampling (grayscale)
    quality,                      // Quality
    TJFLAG_NOREALLOC              // Use provided buffer
  );

  if (result != 0) {
    LOG_ERROR(FRAME_CANVAS, "tjCompress2 failed: " << tjGetErrorStr());
    return 0;
  }

  return jpegSize;
}

void FrameCanvas::setBackgroundColor(const uint8_t grayscaleValue) const {
  const TFloat value = grayscaleToFloat(grayscaleValue);
  std::fill_n(rawBuffer_.get(), totalPixels_, value);
}

void FrameCanvas::drawText(const std::string& text, const size_t x, const size_t y, const uint8_t grayscaleValue) {
  const TFloat value = grayscaleToFloat(grayscaleValue);

  for (size_t i = 0; i < text.length(); ++i) {
    const size_t charX = x + (i * 8); // 8 pixels per character width
    if (charX + 8 > width_) break; // Stop if character would go beyond canvas width

    drawCharacter(text[i], charX, y, value);
  }
}

TFloat FrameCanvas::grayscaleToFloat(const uint8_t grayscaleValue) const {
  // Convert grayscale (0-255) to TFloat range (0.0 to π)
  // This maps grayscale values to the expected range for video processing
  return static_cast<TFloat>(grayscaleValue) * (M_PI / 255.0f);
}

void FrameCanvas::drawCharacter(const char c, const size_t x, const size_t y, const TFloat value) {
  // Simple 8x8 bitmap font data for basic ASCII characters (32-126)
  // Each character is represented as 8 bytes, each byte representing one row
  static const uint8_t font8x8[95][8] = {
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, // ' ' (space)
    {0x18, 0x3C, 0x3C, 0x18, 0x18, 0x00, 0x18, 0x00}, // '!'
    {0x36, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, // '"'
    {0x36, 0x36, 0x7F, 0x36, 0x7F, 0x36, 0x36, 0x00}, // '#'
    {0x0C, 0x3E, 0x03, 0x1E, 0x30, 0x1F, 0x0C, 0x00}, // '$'
    {0x00, 0x63, 0x33, 0x18, 0x0C, 0x66, 0x63, 0x00}, // '%'
    {0x1C, 0x36, 0x1C, 0x6E, 0x3B, 0x33, 0x6E, 0x00}, // '&'
    {0x06, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00}, // '''
    {0x18, 0x0C, 0x06, 0x06, 0x06, 0x0C, 0x18, 0x00}, // '('
    {0x06, 0x0C, 0x18, 0x18, 0x18, 0x0C, 0x06, 0x00}, // ')'
    {0x00, 0x66, 0x3C, 0xFF, 0x3C, 0x66, 0x00, 0x00}, // '*'
    {0x00, 0x0C, 0x0C, 0x3F, 0x0C, 0x0C, 0x00, 0x00}, // '+'
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x06, 0x00}, // ','
    {0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00}, // '-'
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x0C, 0x00}, // '.'
    {0x60, 0x30, 0x18, 0x0C, 0x06, 0x03, 0x01, 0x00}, // '/'
    {0x3E, 0x63, 0x73, 0x7B, 0x6F, 0x67, 0x3E, 0x00}, // '0'
    {0x0C, 0x0E, 0x0C, 0x0C, 0x0C, 0x0C, 0x3F, 0x00}, // '1'
    {0x1E, 0x33, 0x30, 0x1C, 0x06, 0x33, 0x3F, 0x00}, // '2'
    {0x1E, 0x33, 0x30, 0x1C, 0x30, 0x33, 0x1E, 0x00}, // '3'
    {0x38, 0x3C, 0x36, 0x33, 0x7F, 0x30, 0x78, 0x00}, // '4'
    {0x3F, 0x03, 0x1F, 0x30, 0x30, 0x33, 0x1E, 0x00}, // '5'
    {0x1C, 0x06, 0x03, 0x1F, 0x33, 0x33, 0x1E, 0x00}, // '6'
    {0x3F, 0x33, 0x30, 0x18, 0x0C, 0x0C, 0x0C, 0x00}, // '7'
    {0x1E, 0x33, 0x33, 0x1E, 0x33, 0x33, 0x1E, 0x00}, // '8'
    {0x1E, 0x33, 0x33, 0x3E, 0x30, 0x18, 0x0E, 0x00}, // '9'
    {0x00, 0x0C, 0x0C, 0x00, 0x00, 0x0C, 0x0C, 0x00}, // ':'
    {0x00, 0x0C, 0x0C, 0x00, 0x00, 0x0C, 0x06, 0x00}, // ';'
    {0x18, 0x0C, 0x06, 0x03, 0x06, 0x0C, 0x18, 0x00}, // '<'
    {0x00, 0x00, 0x3F, 0x00, 0x00, 0x3F, 0x00, 0x00}, // '='
    {0x06, 0x0C, 0x18, 0x30, 0x18, 0x0C, 0x06, 0x00}, // '>'
    {0x1E, 0x33, 0x30, 0x18, 0x0C, 0x00, 0x0C, 0x00}, // '?'
    {0x3E, 0x63, 0x7B, 0x7B, 0x7B, 0x03, 0x1E, 0x00}, // '@'
    {0x0C, 0x1E, 0x33, 0x33, 0x3F, 0x33, 0x33, 0x00}, // 'A'
    {0x3F, 0x66, 0x66, 0x3E, 0x66, 0x66, 0x3F, 0x00}, // 'B'
    {0x3C, 0x66, 0x03, 0x03, 0x03, 0x66, 0x3C, 0x00}, // 'C'
    {0x1F, 0x36, 0x66, 0x66, 0x66, 0x36, 0x1F, 0x00}, // 'D'
    {0x7F, 0x46, 0x16, 0x1E, 0x16, 0x46, 0x7F, 0x00}, // 'E'
    {0x7F, 0x46, 0x16, 0x1E, 0x16, 0x06, 0x0F, 0x00}, // 'F'
    {0x3C, 0x66, 0x03, 0x03, 0x73, 0x66, 0x7C, 0x00}, // 'G'
    {0x33, 0x33, 0x33, 0x3F, 0x33, 0x33, 0x33, 0x00}, // 'H'
    {0x1E, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x1E, 0x00}, // 'I'
    {0x78, 0x30, 0x30, 0x30, 0x33, 0x33, 0x1E, 0x00}, // 'J'
    {0x67, 0x66, 0x36, 0x1E, 0x36, 0x66, 0x67, 0x00}, // 'K'
    {0x0F, 0x06, 0x06, 0x06, 0x46, 0x66, 0x7F, 0x00}, // 'L'
    {0x63, 0x77, 0x7F, 0x7F, 0x6B, 0x63, 0x63, 0x00}, // 'M'
    {0x63, 0x67, 0x6F, 0x7B, 0x73, 0x63, 0x63, 0x00}, // 'N'
    {0x1C, 0x36, 0x63, 0x63, 0x63, 0x36, 0x1C, 0x00}, // 'O'
    {0x3F, 0x66, 0x66, 0x3E, 0x06, 0x06, 0x0F, 0x00}, // 'P'
    {0x1E, 0x33, 0x33, 0x33, 0x3B, 0x1E, 0x38, 0x00}, // 'Q'
    {0x3F, 0x66, 0x66, 0x3E, 0x36, 0x66, 0x67, 0x00}, // 'R'
    {0x1E, 0x33, 0x07, 0x0E, 0x38, 0x33, 0x1E, 0x00}, // 'S'
    {0x3F, 0x2D, 0x0C, 0x0C, 0x0C, 0x0C, 0x1E, 0x00}, // 'T'
    {0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x3F, 0x00}, // 'U'
    {0x33, 0x33, 0x33, 0x33, 0x33, 0x1E, 0x0C, 0x00}, // 'V'
    {0x63, 0x63, 0x63, 0x6B, 0x7F, 0x77, 0x63, 0x00}, // 'W'
    {0x63, 0x63, 0x36, 0x1C, 0x1C, 0x36, 0x63, 0x00}, // 'X'
    {0x33, 0x33, 0x33, 0x1E, 0x0C, 0x0C, 0x1E, 0x00}, // 'Y'
    {0x7F, 0x63, 0x31, 0x18, 0x4C, 0x66, 0x7F, 0x00}, // 'Z'
  };

  // Check bounds
  if (x + 8 > width_ || y + 8 > height_) return;

  // Get character index (ASCII 32-126 mapped to 0-94)
  int charIndex = static_cast<int>(c) - 32;
  if (charIndex < 0 || charIndex >= 95) {
    charIndex = 0; // Default to space for unsupported characters
  }

  // Draw the character pixel by pixel
  for (int row = 0; row < 8; ++row) {
    if (y + row >= height_) break;

    uint8_t rowData = font8x8[charIndex][row];
    for (int col = 0; col < 8; ++col) {
      if (x + col >= width_) break;

      // Check if pixel should be set (bit is 1)
      if (rowData & (0x80 >> col)) {
        const size_t pixelIndex = (y + row) * width_ + (x + col);
        rawBuffer_[pixelIndex] = value;
      }
    }
  }
}

} // namespace IQVideoProcessor::Pipeline
