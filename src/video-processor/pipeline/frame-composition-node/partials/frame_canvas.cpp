#include "./frame_canvas.h"
#include "logging/logging.h"
#include <algorithm>
#include <cassert>
#include <cmath>
#include <cstring>
#include <fstream>
#include <iostream>
#include <turbojpeg.h>

namespace IQVideoProcessor::Pipeline {

FrameCanvas::~FrameCanvas() {
  // Clean up TurboJPEG compressor
  if (tjHandle_) {
    tjD<PERSON>roy(tjHandle_);
    tjHandle_ = nullptr;
  }
}

FrameCanvas::FrameCanvas(const size_t width, const size_t height): width_(width), height_(height) {
  totalPixels_ = width * height;
  assert(totalPixels_ > 0);

  // Pre-allocate raw buffer
  rawBuffer_ = std::make_unique<TFloat[]>(totalPixels_);
  std::fill_n(rawBuffer_.get(), totalPixels_, 0);

  // Pre-allocate grayscale conversion buffer
  grayscaleBuffer_ = std::make_unique<uint8_t[]>(totalPixels_);

  minRenderBufferSize_ = totalPixels_ << 1;

  // Initialize TurboJPEG compressor once
  tjHandle_ = tjInitCompress();
  if (!tjHandle_) {
    LOG_ERROR(FRAME_CANVAS, "failed to initialize TurboJPEG compressor: " << tjGetErrorStr());
  }
}

size_t FrameCanvas::getMinRenderBufferSize() const {
  return minRenderBufferSize_;
}

void FrameCanvas::setVideoLineRawData(const size_t lineIndex, const TFloat *data, const size_t dataSize) {
  if (lineIndex >= height_ || dataSize == 0) return;

  // Calculate the starting position in the raw buffer for this line
  TFloat* lineStart = &rawBuffer_.get()[lineIndex * width_];

  if (dataSize == width_) {
    std::memcpy(lineStart, data, width_ * sizeof(TFloat)); // Direct copy if sizes match
    return;
  }

  // Linear interpolation resize
  if (dataSize == 1) {
    std::fill_n(lineStart, width_, data[0]);     // Special case: fill entire line with single value
    return;
  }

  const auto scale = static_cast<TFloat>(dataSize - 1) / static_cast<TFloat>(width_ - 1);

  for (size_t i = 0; i < width_; ++i) {
    const auto pos = static_cast<TFloat>(i) * scale;
    const auto idx = static_cast<size_t>(pos);
    const TFloat frac = pos - static_cast<TFloat>(idx);

    if (idx >= dataSize - 1) {
      lineStart[i] = data[dataSize - 1];
    } else {
      lineStart[i] = data[idx] * (static_cast<TFloat>(1.0) - frac) + data[idx + 1] * frac;
    }
  }
}

std::optional<size_t> FrameCanvas::render(uint8_t* outputBuffer, const size_t outputBufferSize, TFloat blackLevel, TFloat whiteLevel) {
  // Convert raw buffer to grayscale using pre-allocated buffer
  const uint8_t* grayscaleData = convertToGrayscale(blackLevel, whiteLevel);

  // Export to JPEG with high quality (100 - to avoid compression)
  auto jpegSize = exportGrayscaleToJpeg(outputBuffer, outputBufferSize, grayscaleData, 75);
  if (!jpegSize) {
    return std::nullopt;
  }

  return jpegSize;
}

const uint8_t* FrameCanvas::convertToGrayscale(const TFloat blackLevel, const TFloat whiteLevel) {
  // Calculate scaling factor
  const TFloat range = whiteLevel - blackLevel;
  if (range <= 0) {
    // Invalid range, fill with middle gray
    std::fill_n(grayscaleBuffer_.get(), totalPixels_, 128);
    return grayscaleBuffer_.get();
  }

  const auto scale = static_cast<TFloat>(255.0f) / range;

  // Convert each pixel using pre-allocated buffer
  for (size_t i = 0; i < totalPixels_; ++i) {
    const auto value = rawBuffer_[i];
    // Clamp to [blackLevel, whiteLevel] range
    if (value <= blackLevel) {
      grayscaleBuffer_[i] = 0;
    } else if (value >= whiteLevel) {
      grayscaleBuffer_[i] = 255;
    } else {
      // Scale to [0, 255] range
      const auto scaled = (value - blackLevel) * scale;
      grayscaleBuffer_[i] = static_cast<uint8_t>(std::round(scaled));
    }
  }

  return grayscaleBuffer_.get();
}

size_t FrameCanvas::exportGrayscaleToJpeg(uint8_t* outputBuffer, const size_t outputBufferSize, const uint8_t* imageData, int quality) const {
  // Sanity checks
  if (!tjHandle_) {
    LOG_ERROR(FRAME_CANVAS, "uninitialized TurboJPEG compressor");
    return 0;
  }

  // Clamp quality to valid range
  quality = std::max(1, std::min(100, quality));

  // Compress the image using provided buffer
  auto jpegSize = outputBufferSize;
  auto* jpegBufPtr = outputBuffer;
  const auto result = tjCompress2(
    tjHandle_,
    imageData,                    // Source image data
    static_cast<int>(width_),     // Image width
    0,                            // Pitch (0 = width * pixel size)
    static_cast<int>(height_),    // Image height
    TJPF_GRAY,                    // Pixel format (grayscale)
    &jpegBufPtr,                  // Output JPEG buffer
    &jpegSize,                    // JPEG size (input: buffer size, output: actual size)
    TJSAMP_GRAY,                  // Subsampling (grayscale)
    quality,                      // Quality
    TJFLAG_NOREALLOC              // Use provided buffer
  );

  if (result != 0) {
    LOG_ERROR(FRAME_CANVAS, "tjCompress2 failed: " << tjGetErrorStr());
    return 0;
  }

  return jpegSize;
}

} // namespace IQVideoProcessor::Pipeline
