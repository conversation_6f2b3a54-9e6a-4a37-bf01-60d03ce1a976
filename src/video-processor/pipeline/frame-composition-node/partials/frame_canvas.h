#pragma once
#include "../../../../types.h"
#include <memory>
#include <string>
#include <optional>
#include <cmath>

namespace IQVideoProcessor::Pipeline {

class FrameCanvas {
public:
  FrameCanvas(size_t width, size_t height);
  ~FrameCanvas();
  void setVideoLineRawData(size_t lineIndex, const TFloat *data, size_t dataSize);
  [[nodiscard]] size_t getMinRenderBufferSize() const;

  /**
   * Set the background color of the entire canvas
   * @param grayscaleValue Grayscale value (0-255) to fill the background with
   */
  void setBackgroundColor(uint8_t grayscaleValue) const;

  /**
   * Draw text on the canvas at the specified position
   * @param text Text string to render
   * @param x X coordinate (left edge of text)
   * @param y Y coordinate (top edge of text)
   * @param grayscaleValue Grayscale value (0-255) for the text color
   */
  void drawText(const std::string& text, size_t x, size_t y, uint8_t grayscaleValue);

  /**
   * Render the frame canvas to JPEG data in the provided buffer
   * @param outputBuffer Pre-allocated buffer for JPEG data
   * @param outputBufferSize Size of the output buffer
   * @param blackLevel Value to map to black (0)
   * @param whiteLevel Value to map to white (255), defaults to π
   * @return Size of rendered JPEG data in bytes, or std::nullopt if rendering failed
   */
  std::optional<size_t> render(uint8_t* outputBuffer, size_t outputBufferSize, TFloat blackLevel, TFloat whiteLevel = M_PI);

private:
  size_t width_;
  size_t height_;
  size_t totalPixels_;
  size_t minRenderBufferSize_;
  std::unique_ptr<TFloat[]> rawBuffer_;
  std::unique_ptr<uint8_t[]> grayscaleBuffer_;

  // TurboJPEG compressor handle (initialized once)
  void *tjHandle_;

  const uint8_t* convertToGrayscale(TFloat blackLevel, TFloat whiteLevel);
  size_t exportGrayscaleToJpeg(uint8_t* outputBuffer, size_t outputBufferSize, const uint8_t* imageData, int quality = 100) const;

  // Helper methods for text rendering
  void drawCharacter(char c, size_t x, size_t y, TFloat value);
  TFloat grayscaleToFloat(uint8_t grayscaleValue) const;
};

}
