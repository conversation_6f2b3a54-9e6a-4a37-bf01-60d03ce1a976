#pragma once
#include "../../../stream-pipeline/stream_node.h"
#include "../../../types.h"
#include "../../utils/countdown/countdown.h"
#include "../iq_demodulation_node_types.h"
#include "../line_detection_node_types.h"
#include "./partials/segment_ave_filter.h"
#include "./partials/segment_pulses_detector.h"
#include "./partials/video_standard_detector.h"
#include "./partials/video_sync_detector.h"
#include "./partials/sync-orchestrator/sync-orchestrator.h"

namespace IQVideoProcessor::Pipeline {

class LineDetectionNode final : public SPipeline::StreamNode<DemodulatedSegment, LineDetectionEvent> {
public:
  explicit LineDetectionNode(SampleRate sampleRate);
  ~LineDetectionNode() override;

private:
  bool process(DemodulatedSegment& segment) override;
  void initLineDetectionEventTemplate(const VideoStandardDetector::Result& standardDetectionResult);
  void initSyncOrchestrator(const VideoStandardDetector::Result& standardDetectionResult);

  struct SegmentProcessingConfig: VideoSyncDetector::Config {
    TFloat signalMinVal;
    TFloat signalMaxVal;
    TFloat signalScale;
  };

  SampleRate sampleRate_;
  SegmentAveFilter segmentSyncDetectionFilter_;
  SegmentPulsesDetector segmentPulsesDetector_{sampleRate_};
  SignalRangeEstimator signalRangeEstimator_{sampleRate_};
  VideoStandardDetector videoStandardDetector_{sampleRate_};
  Countdown<> standardDetectorRetryCountdown_{sampleRate_};
  SyncOrchestrator syncOrchestrator_{sampleRate_};

  // Pointer to the current segment being processed, for reference in callbacks
  DemodulatedSegment* currentSegment_{nullptr};
  LineDetectionEvent currentEvent_{};
};

} // namespace IQVideoProcessor::Pipeline
