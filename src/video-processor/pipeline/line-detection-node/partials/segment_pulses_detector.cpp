#include "./segment_pulses_detector.h"
#include "../../../helpers/helpers.h"

namespace IQVideoProcessor::Pipeline {

SegmentPulsesDetector::SegmentPulsesDetector(const SampleRate sampleRate): sampleRate_(sampleRate) {
  detectedVideoSyncPulses_.resize(1000); // Preallocate for efficiency
}

std::tuple<const std::vector<VideoSyncPulse> &, double>SegmentPulsesDetector::process(const AveFilteredDemodulatedSegment &segment) {
  const auto processingEndPosition = detectSegmentVideoSyncPulses(segment);
  return {detectedVideoSyncPulses_, processingEndPosition};
}

SegmentPulsesDetector::SegmentProcessingConfig SegmentPulsesDetector::getSegmentProcessingConfig(const AveFilteredDemodulatedSegment &segment) {
  const auto [minSampleValue, maxSampleValue] = Helpers::getMinMax(&segment.data[segment.effectiveOffset], segment.effectiveSamples);

  auto config = SegmentProcessingConfig{};
  config.signalMinVal = minSampleValue;
  config.signalMaxVal = maxSampleValue;
  config.signalScale = maxSampleValue - minSampleValue;
  // Configure sync detection parameters based on the signal range and segment properties
  config.pulseThresholdTrigValueDelta = config.signalScale * static_cast<TFloat>(0.10); // 10% of the signal range
  config.pulseThresholdLength = static_cast<TFloat>(segment.aveSize) / static_cast<TFloat>(2); // Half of the average size of a filter
  config.pulseSyncValue = minSampleValue + config.signalScale * static_cast<TFloat>(0.18); // 18% of the signal range
  return config;
}

double SegmentPulsesDetector::detectSegmentVideoSyncPulses(const AveFilteredDemodulatedSegment &filteredSegment) {
  detectedVideoSyncPulses_.clear(); // Clear previous detections

  const auto spc = getSegmentProcessingConfig(filteredSegment);
  const auto absolutePosition = static_cast<double>(filteredSegment.effectiveStartPosition);

  VideoSyncDetector videoSyncDetector(
    &filteredSegment.data[filteredSegment.effectiveOffset],
    // Getting as many as we can, minus half the filter size to ensure we operate within valid data
    filteredSegment.effectiveSamples + filteredSegment.effectiveOffset - filteredSegment.halfAveSize - 1,
    sampleRate_,
    spc
  );

  TFloat fromPosition = nextSegmentFromPosition_; // Continue from last position
  const auto toPosition = static_cast<TFloat>(filteredSegment.effectiveSamples);
  while (videoSyncDetector.findNext(fromPosition)) {
    const auto& result = videoSyncDetector.getResult();
    detectedVideoSyncPulses_.push_back(VideoSyncPulse::fromSyncResult(result, absolutePosition));
    fromPosition = result.risingFrontPosition; // Move to the end of the detected pulse for next search

    if (fromPosition >= toPosition) {
      nextSegmentFromPosition_ = fromPosition - toPosition; // Store overflow for next segment
      return absolutePosition + fromPosition; // Return the position up to which we've processed
    }
  }
  nextSegmentFromPosition_ = 0; // Reset if we haven't found anything within this segment
  return absolutePosition + toPosition - 1.0; // Return the end of the processed segment
}

}