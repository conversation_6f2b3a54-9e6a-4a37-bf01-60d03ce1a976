#pragma once
#include "./segment_ave_filter.h"
#include "./video_sync_pulse.h"

namespace IQVideoProcessor::Pipeline {

class SegmentPulsesDetector {
public:
  explicit SegmentPulsesDetector(SampleRate sampleRate);
  ~SegmentPulsesDetector() = default;

  std::tuple<const std::vector<VideoSyncPulse>&, double> process(const AveFilteredDemodulatedSegment &segment);

private:
  struct SegmentProcessingConfig: VideoSyncDetector::Config {
    TFloat signalMinVal;
    TFloat signalMaxVal;
    TFloat signalScale;
  };

  SampleRate sampleRate_;
  TFloat nextSegmentFromPosition_{0};

  SegmentProcessingConfig getSegmentProcessingConfig(const AveFilteredDemodulatedSegment &segment);
  double detectSegmentVideoSyncPulses(const AveFilteredDemodulatedSegment &filteredSegment);

  std::vector<VideoSyncPulse> detectedVideoSyncPulses_{};
};

}
