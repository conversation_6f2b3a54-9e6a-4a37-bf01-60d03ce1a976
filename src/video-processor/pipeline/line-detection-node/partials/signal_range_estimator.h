#pragma once
#include "../../../../types.h"
#include "../../../utils/range/range.h"

namespace IQVideoProcessor::Pipeline {

enum EstimatedPulseType {
  ES_UNKNOWN_SYNC_PULSE = 0,
  ES_HORIZONTAL_OR_EQUALIZING_SYNC_PULSE = 100,
  ES_VERTICAL_SYNC_PULSE = 200,
  ES_VERTICAL_LONG_SYNC_PULSE = 300,
};

enum EstimatedDistanceType {
  ES_UNKNOWN_DISTANCE = 0,
  ES_HORIZONTAL_DISTANCE = 100,
  ES_HALF_HORIZONTAL_DISTANCE = 200,
  ES_33PERCENT_HORIZONTAL_DISTANCE = 300,
  ES_70PERCENT_HORIZONTAL_DISTANCE = 400,
};

enum PulseComparison {
  LEFT_LESS_THAN_RIGHT = -1,
  LEFT_EQUAL_TO_RIGHT = 0,
  LEFT_GREATER_THAN_RIGHT = 1,
};

// TODO Rename to SignalRangeClassificator
class SignalRangeEstimator {
public:
  explicit SignalRangeEstimator(SampleRate sampleRate);
  ~SignalRangeEstimator() = default;

  [[nodiscard]] EstimatedPulseType estimatePulseByWidth(TFloat pulseWidth) const;
  [[nodiscard]] EstimatedDistanceType estimateSignalDistance(TFloat distance) const;
  [[nodiscard]] bool isHorizontalLineDistance(TFloat distance) const;
  [[nodiscard]] bool isHalfHorizontalLineDistance(TFloat distance) const;
  [[nodiscard]] bool is70PercentHorizontalLineDistance(TFloat distance) const;
  [[nodiscard]] bool is33PercentHorizontalLineDistance(TFloat distance) const;
  [[nodiscard]] bool areEqualPulsesByWidth(TFloat pulseWidth1, TFloat pulseWidth2) const;
  [[nodiscard]] PulseComparison comparePulseByWidth(TFloat pulseWidth1, TFloat pulseWidth2) const;

protected:
  Range<TFloat> horizontalOrEqualizingPulseWidthRange_{}; // 1.5-7 microseconds
  Range<TFloat> verticalSyncPulseWidthRange_{}; // 26-30 microseconds
  Range<TFloat> verticalSyncLongPulseWidthRange_{}; // 440-500 microseconds
  Range<TFloat> horizontalLineDistanceRange_{}; // ~62.3-65.2 microseconds
  Range<TFloat> horizontalLineHalfDistanceRange_{}; // ~30.0-35.0 microseconds
  Range<TFloat> horizontalLine70PercentDistanceRange_{}; // ~42.0-46.0 microseconds
  Range<TFloat> horizontalLine33PercentDistanceRange_{}; // ~18.0-22.0 microseconds

  SampleRate sampleRate_;
};

}