#pragma once
#include "./video-sync-encoder.h"

// PAL standard: https://blog.retroleum.co.uk/electronics-articles/pal-tv-timing-and-voltages/
// Total lines: 625
// Visible lines: 576 (288 per field typical)
// Frame rate: 25 fps (50 fields per second)
// Horizontal frequency: 15.625 kHz
// Line duration: 64 µs (1 / 15625 Hz)
// Horizontal sync pulse: ~4 µs
// Equalizing pulses: 2 µs low + 30 µs (half-line, 32 µs center-to-center)
// Vertical (long/broad) sync pulse: 30 µs low + 2 µs (half-line, 32 µs center-to-center)
// Field 1 (top): 6 pre-eq, 5 vertical (broad), 5 post-eq
// Field 2 (bottom): 5 pre-eq, 5 vertical (broad), 4 post-eq

namespace IQVideoProcessor::Pipeline::SyncEncoders {

class PALSyncEncoder final : public VideoSyncEncoder {
public:
  explicit PALSyncEncoder(const VideoSyncEncoder::Config& config);
  ~PALSyncEncoder() override = default;

  void transit(Transition transition) override;

private:
  void fillTopOrBottomFieldVerticalEncoders(size_t &startIdx, size_t &lastIdx, bool isTopField);
  void fillTopFieldPostEqualizingEncoders(size_t &startIdx);
  void fillTopOrBottomFieldHorizontalEncoders(size_t &startIdx, bool isTopField);
  void fillTopOrBottomFieldPreEqualizingEncoders(size_t &startIdx, size_t &lastIdx, bool isTopField);
  void fillBottomFieldPostEqualizingEncoders(size_t &startIdx);

  // Distances in samples
  double lineDistance_;
  double halfLineDistance_;
  // Distances using center positions of impulses (center-to-center rather than falling edges)
  double lastVerticalToEqualizingDistance_;
  double lastEqualizingToTopFieldHorizontalDistance_;
  double lastEqualizingToBottomFieldHorizontalDistance_;
  double lastEqualizingToVerticalDistance_;
  double lastBottomFieldHorizontalToEqualingDistance_;
  double lastTopFieldHorizontalToEqualingDistance_;

  // Tolerance
  double lineDistanceTolerance_;
  double halfLineDistanceTolerance_;

  // Encoder indices for transitions
  size_t topFieldVerticalStartIdx_{0};
  size_t topFieldVerticalLastIdx_{0};
  size_t topFieldPostEqualizingStartIdx_{0};
  size_t topFieldHorizontalStartIdx_{0};
  size_t topFieldPreEqualizingStartIdx_{0};
  size_t topFieldPreEqualizingLastIdx_{0};
  size_t bottomFieldVerticalStartIdx_{0};
  size_t bottomFieldVerticalLastIdx_{0};
  size_t bottomFieldPostEqualizingStartIdx_{0};
  size_t bottomFieldHorizontalStartIdx_{0};
  size_t bottomFieldPreEqualizingStartIdx_{0};
  size_t bottomFieldPreEqualizingLastIdx_{0};
};

} // namespace IQVideoProcessor::Pipeline::SyncEncoders
