#pragma once

#include "../../../../signal-processing/sync-by-frwd/sync_by_frwd.h"

namespace IQVideoProcessor::Pipeline {

class VideoSyncDetector {
public:
  struct Config {
    TFloat pulseSyncValue;
    TFloat pulseThresholdTrigValueDelta;
    TFloat pulseThresholdLength;
  };

  struct Result {
    TFloat fallingFrontPosition{0};
    TFloat risingFrontPosition{0};
    TFloat centerPosition{0};
    TFloat width{0};
  };

  explicit VideoSyncDetector(const TFloat *data, size_t dataSize, SampleRate sampleRate, const Config &config);
  ~VideoSyncDetector() = default;

  bool findNext(TFloat fromPosition);
  [[nodiscard]] const Result& getResult() const;

private:
  SignalProcessing::SyncByFrwd syncByFrwd_;
  Config config_;
  Result result_;
  SampleRate sampleRate_;
  TFloat pulseMaxWidth_;
  TFloat maxSearchPosition_;
  TFloat foundPosition_{0};

  bool syncBy(int frontType, TFloat fromPosition, TFloat samples);
};

}
