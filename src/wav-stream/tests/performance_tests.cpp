#include "../wav_stream.h"
#include <iostream>
#include <fstream>
#include <vector>
#include <chrono>
#include <cstring>
#include <memory>

// Performance test utilities
class PerformanceTestUtils {
public:
    // Create a large WAV file for performance testing
    static bool createPerformanceWAV(const std::string& filename, 
                                    uint32_t sampleRate = 44100,
                                    uint32_t numSamples = 1000000) {
        
        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        uint16_t bitsPerSample = 16;
        uint16_t numChannels = 2;
        uint32_t dataSize = numSamples * numChannels * (bitsPerSample / 8);
        uint32_t fileSize = 36 + dataSize;
        
        // Write WAV header
        file.write("RIFF", 4);
        file.write(reinterpret_cast<const char*>(&fileSize), 4);
        file.write("WAVE", 4);
        file.write("fmt ", 4);
        
        uint32_t fmtSize = 16;
        file.write(reinterpret_cast<const char*>(&fmtSize), 4);
        
        uint16_t audioFormat = 1;
        file.write(reinterpret_cast<const char*>(&audioFormat), 2);
        file.write(reinterpret_cast<const char*>(&numChannels), 2);
        file.write(reinterpret_cast<const char*>(&sampleRate), 4);
        
        uint32_t byteRate = sampleRate * numChannels * (bitsPerSample / 8);
        file.write(reinterpret_cast<const char*>(&byteRate), 4);
        
        uint16_t blockAlign = numChannels * (bitsPerSample / 8);
        file.write(reinterpret_cast<const char*>(&blockAlign), 2);
        file.write(reinterpret_cast<const char*>(&bitsPerSample), 2);
        
        file.write("data", 4);
        file.write(reinterpret_cast<const char*>(&dataSize), 4);
        
        // Write sample data efficiently
        std::vector<int16_t> buffer(8192); // 4K samples worth
        for (uint32_t i = 0; i < numSamples; i += 4096) {
            uint32_t samplesThisChunk = std::min(4096u, numSamples - i);
            
            for (uint32_t j = 0; j < samplesThisChunk; ++j) {
                uint32_t sampleIndex = i + j;
                buffer[j * 2] = static_cast<int16_t>(sampleIndex % 32767);     // I
                buffer[j * 2 + 1] = static_cast<int16_t>((sampleIndex * 3) % 32767); // Q
            }
            
            file.write(reinterpret_cast<const char*>(buffer.data()), 
                      samplesThisChunk * 2 * sizeof(int16_t));
        }
        
        file.close();
        return true;
    }
    
    // Cleanup test files
    static void cleanup(const std::vector<std::string>& filenames) {
        for (const auto& filename : filenames) {
            std::remove(filename.c_str());
        }
    }
};

// Test file opening performance
int test_open_performance() {
    std::cout << "\n--- Testing File Opening Performance ---" << std::endl;
    
    const std::string testFile = "test_open_perf.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create test WAV file
        if (!PerformanceTestUtils::createPerformanceWAV(testFile, 44100, 100000)) {
            std::cout << "❌ Failed to create performance test WAV file" << std::endl;
            return 1;
        }
        
        // Measure open performance
        const int numIterations = 100;
        auto start = std::chrono::high_resolution_clock::now();
        
        for (int i = 0; i < numIterations; ++i) {
            WAVIQStream stream(testFile);
            if (!stream.open()) {
                std::cout << "❌ Failed to open file on iteration " << i << std::endl;
                PerformanceTestUtils::cleanup(testFiles);
                return 1;
            }
            stream.close();
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        double avgOpenTime = static_cast<double>(duration.count()) / numIterations / 1000.0; // ms
        
        std::cout << "✓ Average file open time: " << avgOpenTime << " ms" << std::endl;
        
        if (avgOpenTime > 50.0) {
            std::cout << "⚠️  Warning: File open time is high (>" << avgOpenTime << " ms)" << std::endl;
        } else {
            std::cout << "✓ File open performance is good" << std::endl;
        }
        
        PerformanceTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in open performance test: " << e.what() << std::endl;
        PerformanceTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test reading performance
int test_read_performance() {
    std::cout << "\n--- Testing Reading Performance ---" << std::endl;
    
    const std::string testFile = "test_read_perf.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create large test WAV file (1M samples)
        const uint32_t totalSamples = 1000000;
        if (!PerformanceTestUtils::createPerformanceWAV(testFile, 44100, totalSamples)) {
            std::cout << "❌ Failed to create large performance test WAV file" << std::endl;
            return 1;
        }
        
        WAVIQStream stream(testFile);
        if (!stream.open()) {
            std::cout << "❌ Failed to open performance test file: " << stream.lastError() << std::endl;
            PerformanceTestUtils::cleanup(testFiles);
            return 1;
        }
        
        // Test reading in chunks
        const size_t chunkSize = 8192; // 8K samples per chunk
        std::vector<RawIQSample> buffer(chunkSize);
        
        auto start = std::chrono::high_resolution_clock::now();
        
        uint32_t samplesRead = 0;
        while (samplesRead < totalSamples) {
            size_t samplesToRead = std::min(chunkSize, static_cast<size_t>(totalSamples - samplesRead));
            
            if (!stream.readSamples(buffer.data(), samplesToRead)) {
                break; // End of stream
            }
            
            samplesRead += samplesToRead;
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        double totalTimeMs = static_cast<double>(duration.count()) / 1000.0;
        double samplesPerSecond = static_cast<double>(samplesRead) / (totalTimeMs / 1000.0);
        double mbPerSecond = (samplesRead * sizeof(RawIQSample)) / (1024.0 * 1024.0) / (totalTimeMs / 1000.0);
        
        std::cout << "✓ Read " << samplesRead << " samples in " << totalTimeMs << " ms" << std::endl;
        std::cout << "✓ Reading rate: " << static_cast<uint64_t>(samplesPerSecond) << " samples/sec" << std::endl;
        std::cout << "✓ Throughput: " << mbPerSecond << " MB/sec" << std::endl;
        
        if (samplesPerSecond < 1000000) { // Less than 1M samples/sec
            std::cout << "⚠️  Warning: Reading performance is low" << std::endl;
        } else {
            std::cout << "✓ Reading performance is good" << std::endl;
        }
        
        PerformanceTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in read performance test: " << e.what() << std::endl;
        PerformanceTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test memory usage
int test_memory_usage() {
    std::cout << "\n--- Testing Memory Usage ---" << std::endl;
    
    const std::string testFile = "test_memory.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create test WAV file
        if (!PerformanceTestUtils::createPerformanceWAV(testFile, 44100, 50000)) {
            std::cout << "❌ Failed to create memory test WAV file" << std::endl;
            return 1;
        }
        
        // Test multiple simultaneous streams
        const int numStreams = 10;
        std::vector<std::unique_ptr<WAVIQStream>> streams;
        
        for (int i = 0; i < numStreams; ++i) {
            auto stream = std::make_unique<WAVIQStream>(testFile);
            if (!stream->open()) {
                std::cout << "❌ Failed to open stream " << i << std::endl;
                PerformanceTestUtils::cleanup(testFiles);
                return 1;
            }
            streams.push_back(std::move(stream));
        }
        
        std::cout << "✓ Successfully opened " << numStreams << " simultaneous streams" << std::endl;
        
        // Read from all streams
        std::vector<RawIQSample> buffer(1000);
        for (int i = 0; i < numStreams; ++i) {
            if (!streams[i]->readSamples(buffer.data(), 1000)) {
                std::cout << "❌ Failed to read from stream " << i << std::endl;
                PerformanceTestUtils::cleanup(testFiles);
                return 1;
            }
        }
        
        std::cout << "✓ Successfully read from all " << numStreams << " streams" << std::endl;
        
        // Close all streams
        streams.clear();
        std::cout << "✓ Successfully closed all streams" << std::endl;
        
        PerformanceTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in memory usage test: " << e.what() << std::endl;
        PerformanceTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test sample packing performance
int test_packing_performance() {
    std::cout << "\n--- Testing Sample Packing Performance ---" << std::endl;
    
    const std::string testFile = "test_packing_perf.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create test WAV file
        if (!PerformanceTestUtils::createPerformanceWAV(testFile, 44100, 100000)) {
            std::cout << "❌ Failed to create packing performance test WAV file" << std::endl;
            return 1;
        }
        
        WAVIQStream stream(testFile);
        if (!stream.open()) {
            std::cout << "❌ Failed to open packing test file: " << stream.lastError() << std::endl;
            PerformanceTestUtils::cleanup(testFiles);
            return 1;
        }
        
        // Test packing performance with different chunk sizes
        std::vector<size_t> chunkSizes = {100, 1000, 10000};
        
        for (size_t chunkSize : chunkSizes) {
            // Reset stream
            stream.close();
            if (!stream.open()) {
                std::cout << "❌ Failed to reopen stream" << std::endl;
                PerformanceTestUtils::cleanup(testFiles);
                return 1;
            }
            
            std::vector<RawIQSample> buffer(chunkSize);
            const int numReads = 50;
            
            auto start = std::chrono::high_resolution_clock::now();
            
            for (int i = 0; i < numReads; ++i) {
                if (!stream.readSamples(buffer.data(), chunkSize)) {
                    break; // End of stream
                }
            }
            
            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
            
            double avgTimePerChunk = static_cast<double>(duration.count()) / numReads / 1000.0; // ms
            double samplesPerMs = static_cast<double>(chunkSize) / avgTimePerChunk;
            
            std::cout << "✓ Chunk size " << chunkSize << ": " 
                     << avgTimePerChunk << " ms/chunk, "
                     << static_cast<uint64_t>(samplesPerMs * 1000) << " samples/sec" << std::endl;
        }
        
        PerformanceTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in packing performance test: " << e.what() << std::endl;
        PerformanceTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Main performance test function
int run_performance_tests() {
    std::cout << "Running WAVIQStream performance tests..." << std::endl;
    
    int failures = 0;
    
    failures += test_open_performance();
    failures += test_read_performance();
    failures += test_memory_usage();
    failures += test_packing_performance();
    
    if (failures == 0) {
        std::cout << "\n🎉 All performance tests passed!" << std::endl;
        std::cout << "\nPerformance Summary:" << std::endl;
        std::cout << "- File opening: <50ms typical" << std::endl;
        std::cout << "- Reading rate: >1M samples/sec" << std::endl;
        std::cout << "- Memory efficiency: Multiple streams supported" << std::endl;
        std::cout << "- Packing overhead: Minimal" << std::endl;
    } else {
        std::cout << "\n❌ " << failures << " performance test(s) failed!" << std::endl;
    }
    
    return failures;
}
