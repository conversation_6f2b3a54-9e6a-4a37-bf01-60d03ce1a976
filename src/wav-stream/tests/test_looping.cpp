#include "../wav_stream.h"
#include <iostream>
#include <fstream>
#include <vector>
#include <cstring>
#include <cassert>

// Looping test utilities
class LoopingTestUtils {
public:
    // Create a small test WAV file with known pattern for looping tests
    static bool createLoopTestWAV(const std::string& filename, 
                                 uint32_t sampleRate = 44100,
                                 uint32_t numSamples = 100) {
        
        std::ofstream file(filename, std::ios::binary);
        if (!file.is_open()) {
            return false;
        }
        
        uint16_t bitsPerSample = 16;
        uint16_t numChannels = 2;
        uint32_t dataSize = numSamples * numChannels * (bitsPerSample / 8);
        uint32_t fileSize = 36 + dataSize;
        
        // Write WAV header
        file.write("RIFF", 4);
        file.write(reinterpret_cast<const char*>(&fileSize), 4);
        file.write("WAVE", 4);
        file.write("fmt ", 4);
        
        uint32_t fmtSize = 16;
        file.write(reinterpret_cast<const char*>(&fmtSize), 4);
        
        uint16_t audioFormat = 1;
        file.write(reinterpret_cast<const char*>(&audioFormat), 2);
        file.write(reinterpret_cast<const char*>(&numChannels), 2);
        file.write(reinterpret_cast<const char*>(&sampleRate), 4);
        
        uint32_t byteRate = sampleRate * numChannels * (bitsPerSample / 8);
        file.write(reinterpret_cast<const char*>(&byteRate), 4);
        
        uint16_t blockAlign = numChannels * (bitsPerSample / 8);
        file.write(reinterpret_cast<const char*>(&blockAlign), 2);
        file.write(reinterpret_cast<const char*>(&bitsPerSample), 2);
        
        file.write("data", 4);
        file.write(reinterpret_cast<const char*>(&dataSize), 4);
        
        // Write predictable pattern: I = sample_index % 1000, Q = (sample_index * 2) % 1000
        for (uint32_t i = 0; i < numSamples; ++i) {
            int16_t i_sample = static_cast<int16_t>(i % 1000);
            int16_t q_sample = static_cast<int16_t>((i * 2) % 1000);
            file.write(reinterpret_cast<const char*>(&i_sample), 2);
            file.write(reinterpret_cast<const char*>(&q_sample), 2);
        }
        
        file.close();
        return true;
    }
    
    // Cleanup test files
    static void cleanup(const std::vector<std::string>& filenames) {
        for (const auto& filename : filenames) {
            std::remove(filename.c_str());
        }
    }
};

// Test basic looping functionality
int test_basic_looping() {
    std::cout << "\n--- Testing Basic Looping Functionality ---" << std::endl;
    
    const std::string testFile = "test_basic_loop.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create small WAV file with 10 samples
        if (!LoopingTestUtils::createLoopTestWAV(testFile, 44100, 10)) {
            std::cout << "❌ Failed to create loop test WAV file" << std::endl;
            return 1;
        }
        
        // Test with looping enabled
        WAVIQStream stream(testFile, true);
        if (!stream.open()) {
            std::cout << "❌ Failed to open loop test file: " << stream.lastError() << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        
        // Verify looping is enabled
        if (!stream.isLooping()) {
            std::cout << "❌ Looping should be enabled" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Looping is enabled" << std::endl;
        
        // Read all 10 samples (should succeed)
        std::vector<RawIQSample> samples1(10);
        if (!stream.readSamples(samples1.data(), 10)) {
            std::cout << "❌ Failed to read first 10 samples" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Successfully read first 10 samples" << std::endl;
        
        // Read another 10 samples (should loop and succeed)
        std::vector<RawIQSample> samples2(10);
        if (!stream.readSamples(samples2.data(), 10)) {
            std::cout << "❌ Failed to read second 10 samples (looping)" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Successfully read second 10 samples (looped)" << std::endl;
        
        // Verify that the samples are identical (looped correctly)
        bool samplesMatch = true;
        for (size_t i = 0; i < 10; ++i) {
            if (samples1[i] != samples2[i]) {
                samplesMatch = false;
                break;
            }
        }
        
        if (!samplesMatch) {
            std::cout << "❌ Looped samples don't match original samples" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Looped samples match original samples" << std::endl;
        
        // Verify total samples read counter
        if (stream.getTotalSamplesRead() != 20) {
            std::cout << "❌ Total samples read should be 20, got " << stream.getTotalSamplesRead() << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Total samples read counter is correct: " << stream.getTotalSamplesRead() << std::endl;
        
        // Stream should still be active
        if (!stream.isActive()) {
            std::cout << "❌ Stream should still be active with looping" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Stream remains active with looping" << std::endl;
        
        LoopingTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in basic looping test: " << e.what() << std::endl;
        LoopingTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test looping disabled behavior
int test_looping_disabled() {
    std::cout << "\n--- Testing Looping Disabled Behavior ---" << std::endl;
    
    const std::string testFile = "test_no_loop.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create small WAV file with 10 samples
        if (!LoopingTestUtils::createLoopTestWAV(testFile, 44100, 10)) {
            std::cout << "❌ Failed to create no-loop test WAV file" << std::endl;
            return 1;
        }
        
        // Test with looping disabled (default)
        WAVIQStream stream(testFile, false);
        if (!stream.open()) {
            std::cout << "❌ Failed to open no-loop test file: " << stream.lastError() << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        
        // Verify looping is disabled
        if (stream.isLooping()) {
            std::cout << "❌ Looping should be disabled" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Looping is disabled" << std::endl;
        
        // Read all 10 samples (should succeed)
        std::vector<RawIQSample> samples1(10);
        if (!stream.readSamples(samples1.data(), 10)) {
            std::cout << "❌ Failed to read first 10 samples" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Successfully read first 10 samples" << std::endl;
        
        // Try to read another 10 samples (should fail - no looping)
        std::vector<RawIQSample> samples2(10);
        if (stream.readSamples(samples2.data(), 10)) {
            std::cout << "❌ Should not be able to read past end without looping" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Correctly failed to read past end without looping" << std::endl;
        
        // Stream should be inactive
        if (stream.isActive()) {
            std::cout << "❌ Stream should be inactive after reaching end" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Stream correctly marked as inactive" << std::endl;
        
        // Verify total samples read counter
        if (stream.getTotalSamplesRead() != 10) {
            std::cout << "❌ Total samples read should be 10, got " << stream.getTotalSamplesRead() << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Total samples read counter is correct: " << stream.getTotalSamplesRead() << std::endl;
        
        LoopingTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in looping disabled test: " << e.what() << std::endl;
        LoopingTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test dynamic looping control
int test_dynamic_looping_control() {
    std::cout << "\n--- Testing Dynamic Looping Control ---" << std::endl;
    
    const std::string testFile = "test_dynamic_loop.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create small WAV file with 5 samples
        if (!LoopingTestUtils::createLoopTestWAV(testFile, 44100, 5)) {
            std::cout << "❌ Failed to create dynamic loop test WAV file" << std::endl;
            return 1;
        }
        
        // Start with looping disabled
        WAVIQStream stream(testFile, false);
        if (!stream.open()) {
            std::cout << "❌ Failed to open dynamic loop test file: " << stream.lastError() << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        
        // Read all 5 samples
        std::vector<RawIQSample> samples1(5);
        if (!stream.readSamples(samples1.data(), 5)) {
            std::cout << "❌ Failed to read first 5 samples" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Read first 5 samples" << std::endl;
        
        // Enable looping dynamically
        stream.setLooping(true);
        if (!stream.isLooping()) {
            std::cout << "❌ Looping should be enabled after setLooping(true)" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Dynamically enabled looping" << std::endl;
        
        // Reset stream to beginning
        if (!stream.reset()) {
            std::cout << "❌ Failed to reset stream: " << stream.lastError() << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Successfully reset stream" << std::endl;
        
        // Now read 10 samples (should loop)
        std::vector<RawIQSample> samples2(10);
        if (!stream.readSamples(samples2.data(), 10)) {
            std::cout << "❌ Failed to read 10 samples with looping enabled" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Successfully read 10 samples with looping" << std::endl;
        
        // Disable looping again
        stream.setLooping(false);
        if (stream.isLooping()) {
            std::cout << "❌ Looping should be disabled after setLooping(false)" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Dynamically disabled looping" << std::endl;
        
        LoopingTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in dynamic looping control test: " << e.what() << std::endl;
        LoopingTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test multiple loop cycles
int test_multiple_loop_cycles() {
    std::cout << "\n--- Testing Multiple Loop Cycles ---" << std::endl;
    
    const std::string testFile = "test_multi_loop.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create small WAV file with 3 samples
        if (!LoopingTestUtils::createLoopTestWAV(testFile, 44100, 3)) {
            std::cout << "❌ Failed to create multi-loop test WAV file" << std::endl;
            return 1;
        }
        
        WAVIQStream stream(testFile, true);
        if (!stream.open()) {
            std::cout << "❌ Failed to open multi-loop test file: " << stream.lastError() << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        
        // Read samples across multiple loop cycles
        const int totalSamplesToRead = 15; // 5 complete cycles of 3 samples
        std::vector<RawIQSample> allSamples(totalSamplesToRead);
        
        if (!stream.readSamples(allSamples.data(), totalSamplesToRead)) {
            std::cout << "❌ Failed to read " << totalSamplesToRead << " samples across multiple loops" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Successfully read " << totalSamplesToRead << " samples across multiple loops" << std::endl;
        
        // Verify that the pattern repeats correctly
        bool patternCorrect = true;
        for (int i = 0; i < totalSamplesToRead; ++i) {
            int expectedIndex = i % 3;
            RawIQSample sample = allSamples[i];
            uint16_t i_part = static_cast<uint16_t>(sample & 0xFFFF);
            uint16_t q_part = static_cast<uint16_t>((sample >> 16) & 0xFFFF);
            
            uint16_t expected_i = static_cast<uint16_t>(expectedIndex % 1000);
            uint16_t expected_q = static_cast<uint16_t>((expectedIndex * 2) % 1000);
            
            if (i_part != expected_i || q_part != expected_q) {
                std::cout << "❌ Pattern mismatch at sample " << i 
                         << ": got I=" << i_part << " Q=" << q_part
                         << ", expected I=" << expected_i << " Q=" << expected_q << std::endl;
                patternCorrect = false;
                break;
            }
        }
        
        if (!patternCorrect) {
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Pattern repeats correctly across multiple loop cycles" << std::endl;
        
        // Verify total samples read
        if (stream.getTotalSamplesRead() != static_cast<uint64_t>(totalSamplesToRead)) {
            std::cout << "❌ Total samples read should be " << totalSamplesToRead 
                     << ", got " << stream.getTotalSamplesRead() << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Total samples read counter is correct: " << stream.getTotalSamplesRead() << std::endl;
        
        LoopingTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in multiple loop cycles test: " << e.what() << std::endl;
        LoopingTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Test reset functionality
int test_reset_functionality() {
    std::cout << "\n--- Testing Reset Functionality ---" << std::endl;
    
    const std::string testFile = "test_reset.wav";
    std::vector<std::string> testFiles = {testFile};
    
    try {
        // Create WAV file with 10 samples
        if (!LoopingTestUtils::createLoopTestWAV(testFile, 44100, 10)) {
            std::cout << "❌ Failed to create reset test WAV file" << std::endl;
            return 1;
        }
        
        WAVIQStream stream(testFile, false); // No looping
        if (!stream.open()) {
            std::cout << "❌ Failed to open reset test file: " << stream.lastError() << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        
        // Read 5 samples
        std::vector<RawIQSample> samples1(5);
        if (!stream.readSamples(samples1.data(), 5)) {
            std::cout << "❌ Failed to read first 5 samples" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Read first 5 samples" << std::endl;
        
        // Reset stream
        if (!stream.reset()) {
            std::cout << "❌ Failed to reset stream: " << stream.lastError() << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Successfully reset stream" << std::endl;
        
        // Read 5 samples again (should be same as first read)
        std::vector<RawIQSample> samples2(5);
        if (!stream.readSamples(samples2.data(), 5)) {
            std::cout << "❌ Failed to read samples after reset" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Successfully read samples after reset" << std::endl;
        
        // Verify samples are identical
        bool samplesMatch = true;
        for (size_t i = 0; i < 5; ++i) {
            if (samples1[i] != samples2[i]) {
                samplesMatch = false;
                break;
            }
        }
        
        if (!samplesMatch) {
            std::cout << "❌ Samples after reset don't match original samples" << std::endl;
            LoopingTestUtils::cleanup(testFiles);
            return 1;
        }
        std::cout << "✓ Samples after reset match original samples" << std::endl;
        
        LoopingTestUtils::cleanup(testFiles);
        return 0;
        
    } catch (const std::exception& e) {
        std::cout << "❌ Exception in reset functionality test: " << e.what() << std::endl;
        LoopingTestUtils::cleanup(testFiles);
        return 1;
    }
}

// Main looping test function
int run_looping_tests() {
    std::cout << "Running WAVIQStream looping tests..." << std::endl;
    
    int failures = 0;
    
    failures += test_basic_looping();
    failures += test_looping_disabled();
    failures += test_dynamic_looping_control();
    failures += test_multiple_loop_cycles();
    failures += test_reset_functionality();
    
    if (failures == 0) {
        std::cout << "\n🎉 All looping tests passed!" << std::endl;
        std::cout << "\nLooping Features Verified:" << std::endl;
        std::cout << "  ✓ Basic looping functionality" << std::endl;
        std::cout << "  ✓ Looping disabled behavior" << std::endl;
        std::cout << "  ✓ Dynamic looping control" << std::endl;
        std::cout << "  ✓ Multiple loop cycles" << std::endl;
        std::cout << "  ✓ Reset functionality" << std::endl;
        std::cout << "  ✓ Total samples read counter" << std::endl;
    } else {
        std::cout << "\n❌ " << failures << " looping test(s) failed!" << std::endl;
    }
    
    return failures;
}
